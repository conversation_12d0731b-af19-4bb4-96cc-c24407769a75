# SoloAgency

This project uses:

- [Ruff](https://github.com/astral-sh/ruff) for linting
- [uv](https://github.com/astral-sh/uv) for package management
- [Toga](https://beeware.org/project/projects/libraries/toga/) for the GUI framework
- [pytest](https://docs.pytest.org/) for testing framework
- [pytest-cov](https://pytest-cov.readthedocs.io/) for test coverage reporting

## Project structure

```
SoloAgency/
├── main.py              # Main application entry point
├── managers/           # Core management modules
│   ├── __init__.py
│   ├── config_manager.py    # Configuration management
│   └── ui_manager.py        # UI state and management
├── pyproject.toml      # Project metadata and dependencies
├── tests/             # Test directory
│   ├── conftest.py    # Test configuration and fixtures
│   └── test_*.py      # Test files
├── .github/           # GitHub configuration
├── .venv/             # Virtual environment
└── docs/             # Documentation
    └── README.md     # This file
```

Key components:
- `main.py` - Application entry point and initialization
- `entities/` - Data models and entities
- `managers/` - Core management modules
  - `config_manager.py` - Handles configuration loading and management
  - `ui_manager.py` - Manages UI state and interactions
- `tests/` - Test suite and fixtures
  - `conftest.py` - Test configuration and shared fixtures
  - `test_*.py` - Individual test modules
- `.github/` - GitHub workflows and configuration
- `docs/` - Project documentation
