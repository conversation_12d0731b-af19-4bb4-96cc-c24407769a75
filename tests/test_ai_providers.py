"""
Tests for AI Providers entities module.
"""

import pytest
from entities.ai_providers import (
    AIProvider,
    AIModel,
    get_models_for_provider,
    get_all_models,
    get_model_by_name,
)


class TestAIProvider:
    """Test cases for AIProvider enum."""

    def test_ai_provider_values(self):
        """Test that all expected AI providers are defined."""
        expected_providers = {
            "none",
            "openai",
            "openai_like",
            "anthropic",
            "azure_ai",
            "bedrock",
            "gemini",
            "open_router",
            "perplexity",
            "together_ai",
        }
        actual_providers = {provider.value for provider in AIProvider}
        assert actual_providers == expected_providers

    def test_ai_provider_enum_access(self):
        """Test accessing AI providers by name."""
        assert AIProvider.OPENAI.value == "openai"
        assert AIProvider.ANTHROPIC.value == "anthropic"
        assert AIProvider.GEMINI.value == "gemini"


class TestAIModel:
    """Test cases for AIModel dataclass."""

    def test_ai_model_creation(self):
        """Test creating an AIModel instance."""
        model = AIModel(
            name="gpt-4o",
            display_name="GPT-4o",
            provider=AIProvider.OPENAI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="Test model",
        )

        assert model.name == "gpt-4o"
        assert model.display_name == "GPT-4o"
        assert model.provider == AIProvider.OPENAI
        assert model.context_length == 128000
        assert model.supports_vision is True
        assert model.supports_function_calling is True
        assert model.supports_streaming is True  # default value
        assert model.description == "Test model"

    def test_ai_model_provider_string_conversion(self):
        """Test that string provider values are converted to enum."""
        model = AIModel(
            name="test-model",
            display_name="Test Model",
            provider="openai",  # string instead of enum
            context_length=4096,
        )

        assert model.provider == AIProvider.OPENAI
        assert isinstance(model.provider, AIProvider)

    def test_ai_model_defaults(self):
        """Test default values for optional fields."""
        model = AIModel(
            name="test-model",
            display_name="Test Model",
            provider=AIProvider.OPENAI,
            context_length=4096,
        )

        assert model.supports_vision is False
        assert model.supports_function_calling is False
        assert model.supports_streaming is True
        assert model.description == ""


class TestProviderModels:
    """Test cases for provider model functions."""

    def test_get_models_for_openai(self):
        """Test getting models for OpenAI provider."""
        models = get_models_for_provider(AIProvider.OPENAI)

        assert len(models) > 0
        assert all(model.provider == AIProvider.OPENAI for model in models)

        # Check for expected models
        model_names = [model.name for model in models]
        assert "gpt-4o" in model_names
        assert "gpt-4o-mini" in model_names
        assert "gpt-3.5-turbo" in model_names

    def test_get_models_for_anthropic(self):
        """Test getting models for Anthropic provider."""
        models = get_models_for_provider(AIProvider.ANTHROPIC)

        assert len(models) > 0
        assert all(model.provider == AIProvider.ANTHROPIC for model in models)

        # Check for expected models
        model_names = [model.name for model in models]
        assert "claude-3-5-sonnet-20241022" in model_names
        assert "claude-3-opus-20240229" in model_names

    def test_get_models_for_gemini(self):
        """Test getting models for Gemini provider."""
        models = get_models_for_provider(AIProvider.GEMINI)

        assert len(models) > 0
        assert all(model.provider == AIProvider.GEMINI for model in models)

        # Check for expected models
        model_names = [model.name for model in models]
        assert "gemini-1.5-pro" in model_names
        assert "gemini-1.5-flash" in model_names

    def test_get_models_for_unknown_provider(self):
        """Test getting models for a provider with no defined models."""
        # This should return an empty list, not raise an error
        models = get_models_for_provider(AIProvider.NONE)
        assert models == []

    def test_get_all_models(self):
        """Test getting all models across all providers."""
        all_models = get_all_models()

        assert len(all_models) > 0

        # Check that we have models from different providers
        providers = {model.provider for model in all_models}
        assert AIProvider.OPENAI in providers
        assert AIProvider.ANTHROPIC in providers
        assert AIProvider.GEMINI in providers

    def test_get_model_by_name_with_provider(self):
        """Test getting a specific model by name and provider."""
        model = get_model_by_name("gpt-4o", AIProvider.OPENAI)

        assert model.name == "gpt-4o"
        assert model.provider == AIProvider.OPENAI
        assert model.display_name == "GPT-4o"

    def test_get_model_by_name_without_provider(self):
        """Test getting a specific model by name across all providers."""
        model = get_model_by_name("gpt-4o")

        assert model.name == "gpt-4o"
        assert model.provider == AIProvider.OPENAI

    def test_get_model_by_name_not_found(self):
        """Test getting a non-existent model raises ValueError."""
        with pytest.raises(ValueError, match="Model 'non-existent-model' not found"):
            get_model_by_name("non-existent-model")

    def test_get_model_by_name_not_found_with_provider(self):
        """Test getting a non-existent model for a specific provider raises ValueError."""
        with pytest.raises(
            ValueError,
            match="Model 'non-existent-model' not found for provider 'openai'",
        ):
            get_model_by_name("non-existent-model", AIProvider.OPENAI)


class TestModelProperties:
    """Test cases for model properties and capabilities."""

    def test_vision_capable_models(self):
        """Test that vision-capable models are correctly identified."""
        all_models = get_all_models()
        vision_models = [model for model in all_models if model.supports_vision]

        assert len(vision_models) > 0

        # Check some known vision-capable models
        vision_model_names = [model.name for model in vision_models]
        assert "gpt-4o" in vision_model_names
        assert "claude-3-5-sonnet-20241022" in vision_model_names
        assert "gemini-1.5-pro" in vision_model_names

    def test_function_calling_models(self):
        """Test that function-calling capable models are correctly identified."""
        all_models = get_all_models()
        function_models = [
            model for model in all_models if model.supports_function_calling
        ]

        assert len(function_models) > 0

        # Most modern models should support function calling
        function_model_names = [model.name for model in function_models]
        assert "gpt-4o" in function_model_names
        assert "claude-3-5-sonnet-20241022" in function_model_names

    def test_context_lengths(self):
        """Test that models have reasonable context lengths."""
        all_models = get_all_models()

        for model in all_models:
            assert model.context_length > 0
            assert isinstance(model.context_length, int)

        # Check some specific context lengths
        gpt4o = get_model_by_name("gpt-4o", AIProvider.OPENAI)
        assert gpt4o.context_length == 128000

        claude_sonnet = get_model_by_name(
            "claude-3-5-sonnet-20241022", AIProvider.ANTHROPIC
        )
        assert claude_sonnet.context_length == 200000

        gemini_pro = get_model_by_name("gemini-1.5-pro", AIProvider.GEMINI)
        assert gemini_pro.context_length == 2000000
