import json
from pathlib import Path
from platformdirs import user_config_dir
import threading
from dataclasses import dataclass, asdict
from enum import Enum

APP_NAME = "SoloAgency"
CONFIG_FILE = "config.json"


class AIProvider(Enum):
    """Enum for supported AI providers."""

    NONE = "none"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GEMINI = "gemini"
    CLAUDE = "claude"
    GROQ = "groq"


@dataclass
class Config:
    """
    Config class for storing application configuration.
    """

    ai_provider: AIProvider = AIProvider.NONE
    openai_api_key: str = ""
    anthropic_api_key: str = ""
    gemini_api_key: str = ""
    claude_api_key: str = ""
    groq_api_key: str = ""

    def __post_init__(self):
        if isinstance(self.ai_provider, str):
            self.ai_provider = AIProvider(self.ai_provider)


class ConfigManager:
    def __init__(self):
        self._app_name = APP_NAME
        self._config_file = CONFIG_FILE
        self._config_path = Path(user_config_dir(APP_NAME)) / CONFIG_FILE
        self._config_path.parent.mkdir(parents=True, exist_ok=True)
        self._config_cache: Config = Config()
        self._lock = threading.Lock()
        self._load_configs()

    def _load_configs(self) -> None:
        """Load configs from file into cache. If file doesn't exist, initialize with empty config."""
        with self._lock:
            try:
                if not self._config_path.exists():
                    self._config_cache = Config()
                    return

                with open(self._config_path, "r") as f:
                    data = json.load(f)
                    self._config_cache = Config(**data)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading config: {e}")
                self._config_cache = Config()

    def _save_configs(self) -> None:
        """Save cached configs to file."""
        data = asdict(self._config_cache)
        # Convert ai_provider enum to its value for JSON serialization
        if isinstance(data["ai_provider"], AIProvider):
            data["ai_provider"] = data["ai_provider"].value
        with open(self._config_path, "w") as f:
            json.dump(data, f, indent=4)

    def get_configs(self) -> Config:
        """Get current cached configs."""
        with self._lock:
            return Config(**asdict(self._config_cache))

    def update_openai_api_key(self, api_key: str) -> None:
        """Update OpenAI API key in config and save to file."""
        with self._lock:
            if not api_key or not api_key.strip():
                raise ValueError("API key cannot be empty")
            self._config_cache.openai_api_key = api_key
            self._save_configs()
