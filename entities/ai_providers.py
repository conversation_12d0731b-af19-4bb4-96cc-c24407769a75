"""
AI Providers and Models entities for SoloAgency.

This module defines the AI providers and their available models.
"""

from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Optional


class AIProvider(Enum):
    """Enum for supported AI providers."""

    NONE = "none"
    OPENAI = "openai"
    OPENAI_LIKE = "openai_like"
    ANTHROPIC = "anthropic"
    AZURE_AI = "azure_ai"
    BEDROCK = "bedrock"
    GEMINI = "gemini"
    OPEN_ROUTER = "open_router"
    PERPLEXITY = "perplexity"
    TOGETHER_AI = "together_ai"


@dataclass
class AIModel:
    """
    Represents an AI model with its properties.
    """

    name: str
    display_name: str
    provider: AIProvider
    context_length: int
    supports_vision: bool = False
    supports_function_calling: bool = False
    supports_streaming: bool = True
    description: str = ""

    def __post_init__(self):
        if isinstance(self.provider, str):
            self.provider = AIProvider(self.provider)


# Define models for each provider
_PROVIDER_MODELS: Dict[AIProvider, List[AIModel]] = {
    AIProvider.OPENAI: [
        AIModel(
            name="gpt-4o",
            display_name="GPT-4o",
            provider=AIProvider.OPENAI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="Most advanced GPT-4 model with vision capabilities",
        ),
        AIModel(
            name="gpt-4o-mini",
            display_name="GPT-4o Mini",
            provider=AIProvider.OPENAI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="Faster and more cost-effective GPT-4o model",
        ),
        AIModel(
            name="gpt-4-turbo",
            display_name="GPT-4 Turbo",
            provider=AIProvider.OPENAI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="High-performance GPT-4 model with large context window",
        ),
        AIModel(
            name="gpt-3.5-turbo",
            display_name="GPT-3.5 Turbo",
            provider=AIProvider.OPENAI,
            context_length=16385,
            supports_function_calling=True,
            description="Fast and efficient model for most tasks",
        ),
    ],
    AIProvider.OPENAI_LIKE: [
        AIModel(
            name="custom-model",
            display_name="Custom OpenAI-Compatible Model",
            provider=AIProvider.OPENAI_LIKE,
            context_length=4096,
            description="Custom model compatible with OpenAI API",
        ),
    ],
    AIProvider.ANTHROPIC: [
        AIModel(
            name="claude-3-5-sonnet-20241022",
            display_name="Claude 3.5 Sonnet",
            provider=AIProvider.ANTHROPIC,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Most intelligent Claude model with vision capabilities",
        ),
        AIModel(
            name="claude-3-5-haiku-20241022",
            display_name="Claude 3.5 Haiku",
            provider=AIProvider.ANTHROPIC,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Fastest Claude model with vision capabilities",
        ),
        AIModel(
            name="claude-3-opus-20240229",
            display_name="Claude 3 Opus",
            provider=AIProvider.ANTHROPIC,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Most powerful Claude model for complex tasks",
        ),
        AIModel(
            name="claude-3-sonnet-20240229",
            display_name="Claude 3 Sonnet",
            provider=AIProvider.ANTHROPIC,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Balanced Claude model for most use cases",
        ),
    ],
    AIProvider.AZURE_AI: [
        AIModel(
            name="gpt-4o",
            display_name="GPT-4o (Azure)",
            provider=AIProvider.AZURE_AI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="GPT-4o deployed on Azure OpenAI Service",
        ),
        AIModel(
            name="gpt-4-turbo",
            display_name="GPT-4 Turbo (Azure)",
            provider=AIProvider.AZURE_AI,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="GPT-4 Turbo deployed on Azure OpenAI Service",
        ),
        AIModel(
            name="gpt-35-turbo",
            display_name="GPT-3.5 Turbo (Azure)",
            provider=AIProvider.AZURE_AI,
            context_length=16385,
            supports_function_calling=True,
            description="GPT-3.5 Turbo deployed on Azure OpenAI Service",
        ),
    ],
    AIProvider.BEDROCK: [
        AIModel(
            name="anthropic.claude-3-5-sonnet-20241022-v2:0",
            display_name="Claude 3.5 Sonnet (Bedrock)",
            provider=AIProvider.BEDROCK,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Claude 3.5 Sonnet on AWS Bedrock",
        ),
        AIModel(
            name="anthropic.claude-3-opus-20240229-v1:0",
            display_name="Claude 3 Opus (Bedrock)",
            provider=AIProvider.BEDROCK,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Claude 3 Opus on AWS Bedrock",
        ),
        AIModel(
            name="amazon.titan-text-premier-v1:0",
            display_name="Amazon Titan Text Premier",
            provider=AIProvider.BEDROCK,
            context_length=32000,
            supports_function_calling=True,
            description="Amazon's flagship text generation model",
        ),
    ],
    AIProvider.GEMINI: [
        AIModel(
            name="gemini-1.5-pro",
            display_name="Gemini 1.5 Pro",
            provider=AIProvider.GEMINI,
            context_length=2000000,
            supports_vision=True,
            supports_function_calling=True,
            description="Google's most capable multimodal model with huge context",
        ),
        AIModel(
            name="gemini-1.5-flash",
            display_name="Gemini 1.5 Flash",
            provider=AIProvider.GEMINI,
            context_length=1000000,
            supports_vision=True,
            supports_function_calling=True,
            description="Fast and efficient multimodal model",
        ),
        AIModel(
            name="gemini-pro",
            display_name="Gemini Pro",
            provider=AIProvider.GEMINI,
            context_length=32768,
            supports_function_calling=True,
            description="Optimized for text-based tasks",
        ),
    ],
    AIProvider.OPEN_ROUTER: [
        AIModel(
            name="anthropic/claude-3.5-sonnet",
            display_name="Claude 3.5 Sonnet (OpenRouter)",
            provider=AIProvider.OPEN_ROUTER,
            context_length=200000,
            supports_vision=True,
            supports_function_calling=True,
            description="Claude 3.5 Sonnet via OpenRouter",
        ),
        AIModel(
            name="openai/gpt-4o",
            display_name="GPT-4o (OpenRouter)",
            provider=AIProvider.OPEN_ROUTER,
            context_length=128000,
            supports_vision=True,
            supports_function_calling=True,
            description="GPT-4o via OpenRouter",
        ),
        AIModel(
            name="google/gemini-pro-1.5",
            display_name="Gemini Pro 1.5 (OpenRouter)",
            provider=AIProvider.OPEN_ROUTER,
            context_length=2000000,
            supports_vision=True,
            supports_function_calling=True,
            description="Gemini Pro 1.5 via OpenRouter",
        ),
    ],
    AIProvider.PERPLEXITY: [
        AIModel(
            name="llama-3.1-sonar-large-128k-online",
            display_name="Llama 3.1 Sonar Large Online",
            provider=AIProvider.PERPLEXITY,
            context_length=127072,
            supports_function_calling=True,
            description="Large model with real-time web search capabilities",
        ),
        AIModel(
            name="llama-3.1-sonar-small-128k-online",
            display_name="Llama 3.1 Sonar Small Online",
            provider=AIProvider.PERPLEXITY,
            context_length=127072,
            supports_function_calling=True,
            description="Efficient model with real-time web search capabilities",
        ),
        AIModel(
            name="llama-3.1-8b-instruct",
            display_name="Llama 3.1 8B Instruct",
            provider=AIProvider.PERPLEXITY,
            context_length=131072,
            supports_function_calling=True,
            description="Fast and efficient instruction-following model",
        ),
    ],
    AIProvider.TOGETHER_AI: [
        AIModel(
            name="meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
            display_name="Llama 3.1 405B Instruct Turbo",
            provider=AIProvider.TOGETHER_AI,
            context_length=131072,
            supports_function_calling=True,
            description="Largest and most capable Llama model",
        ),
        AIModel(
            name="meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
            display_name="Llama 3.1 70B Instruct Turbo",
            provider=AIProvider.TOGETHER_AI,
            context_length=131072,
            supports_function_calling=True,
            description="High-performance Llama model for complex tasks",
        ),
        AIModel(
            name="mistralai/Mixtral-8x7B-Instruct-v0.1",
            display_name="Mixtral 8x7B Instruct",
            provider=AIProvider.TOGETHER_AI,
            context_length=32768,
            supports_function_calling=True,
            description="Efficient mixture-of-experts model",
        ),
    ],
}


def get_models_for_provider(provider: AIProvider) -> List[AIModel]:
    """
    Get all available models for a specific AI provider.

    Args:
        provider: The AI provider to get models for

    Returns:
        List of AIModel instances for the provider
    """
    return _PROVIDER_MODELS.get(provider, [])


def get_all_models() -> List[AIModel]:
    """
    Get all available models across all providers.

    Returns:
        List of all AIModel instances
    """
    all_models = []
    for models in _PROVIDER_MODELS.values():
        all_models.extend(models)
    return all_models


def get_model_by_name(model_name: str, provider: Optional[AIProvider] = None) -> AIModel:
    """
    Get a specific model by name, optionally filtered by provider.

    Args:
        model_name: The name of the model to find
        provider: Optional provider to filter by

    Returns:
        The AIModel instance if found

    Raises:
        ValueError: If the model is not found
    """
    if provider:
        models = get_models_for_provider(provider)
    else:
        models = get_all_models()

    for model in models:
        if model.name == model_name:
            return model

    raise ValueError(
        f"Model '{model_name}' not found"
        + (f" for provider '{provider.value}'" if provider else "")
    )
