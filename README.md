# SoloAgency

Build full-scale ad campaigns using AI agent flows — all from a single platform, with just one person. Your all-in-one virtual advertising agency.

## Prerequisites

- Python 3.13 or higher
- [uv](https://github.com/astral-sh/uv) package manager

## Development Setup

1. Clone the repository:

```bash
git clone https://github.com/yourusername/SoloAgency.git
cd SoloAgency
```

2. Install dependencies and access venv:

```bash
uv sync
source .venv/bin/activate
```

## Running the Application

To run the application:

```bash
uv run main.py
```

## Development

The project uses:
- [Ruff](https://github.com/astral-sh/ruff) for linting
- [uv](https://github.com/astral-sh/uv) for package management
- [Toga](https://beeware.org/project/projects/libraries/toga/) for the GUI framework
- [pytest](https://docs.pytest.org/) for testing framework
- [pytest-cov](https://pytest-cov.readthedocs.io/) for test coverage reporting

## Testing

To run the test suite:

```bash
pytest
```

## Project Structure

```
SoloAgency/
├── main.py              # Main application entry point
├── managers/           # Core management modules
│   ├── __init__.py
│   ├── config_manager.py    # Configuration management
│   └── ui_manager.py        # UI state and management
├── pyproject.toml      # Project metadata and dependencies
├── tests/             # Test directory
│   ├── conftest.py    # Test configuration and fixtures
│   └── test_*.py      # Test files
├── .github/           # GitHub configuration
├── .venv/             # Virtual environment
└── docs/             # Documentation
    └── README.md     # This file
```

Key components:
- `main.py` - Application entry point and initialization
- `managers/` - Core management modules
  - `config_manager.py` - Handles configuration loading and management
  - `ui_manager.py` - Manages UI state and interactions
- `tests/` - Test suite and fixtures
  - `conftest.py` - Test configuration and shared fixtures
  - `test_*.py` - Individual test modules
- `.github/` - GitHub workflows and configuration
- `docs/` - Project documentation

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
