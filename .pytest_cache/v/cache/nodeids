["tests/test_ai_providers.py::TestAIModel::test_ai_model_creation", "tests/test_ai_providers.py::TestAIModel::test_ai_model_defaults", "tests/test_ai_providers.py::TestAIModel::test_ai_model_provider_string_conversion", "tests/test_ai_providers.py::TestAIProvider::test_ai_provider_enum_access", "tests/test_ai_providers.py::TestAIProvider::test_ai_provider_values", "tests/test_ai_providers.py::TestModelProperties::test_context_lengths", "tests/test_ai_providers.py::TestModelProperties::test_function_calling_models", "tests/test_ai_providers.py::TestModelProperties::test_vision_capable_models", "tests/test_ai_providers.py::TestProviderModels::test_get_all_models", "tests/test_ai_providers.py::TestProviderModels::test_get_model_by_name_not_found", "tests/test_ai_providers.py::TestProviderModels::test_get_model_by_name_not_found_with_provider", "tests/test_ai_providers.py::TestProviderModels::test_get_model_by_name_with_provider", "tests/test_ai_providers.py::TestProviderModels::test_get_model_by_name_without_provider", "tests/test_ai_providers.py::TestProviderModels::test_get_models_for_anthropic", "tests/test_ai_providers.py::TestProviderModels::test_get_models_for_gemini", "tests/test_ai_providers.py::TestProviderModels::test_get_models_for_openai", "tests/test_ai_providers.py::TestProviderModels::test_get_models_for_unknown_provider", "tests/test_config_manager.py::test_config_serialization", "tests/test_config_manager.py::test_initialization", "tests/test_config_manager.py::test_load_invalid_json", "tests/test_config_manager.py::test_save_and_load_configs", "tests/test_config_manager.py::test_update_openai_api_key", "tests/test_config_manager.py::test_update_openai_api_key_empty"]